import React, {useState, useEffect, useRef, forwardRef, useImperativeHandle} from 'react';
import {
  Card,
  Table,
  Typography,
  Switch,
  Tooltip,
  Button,
  Input,
  InputNumber,
  Popover,
  Form,
  message,
  Space,
  Modal,
} from 'antd';
import {
  QuestionCircleOutlined,
  DeleteOutlined,
  PlusOutlined,
  SaveOutlined,
  EditOutlined,
  RobotOutlined,
} from '@ant-design/icons';
import {useTranslation} from 'react-i18next';
import './TaskEvaluationDisplayCard.scss';
import {
  createRoleplayTask,
  updateRoleplayTask,
  deleteRoleplayTask,
  createTasksFromPrompt,
} from '@src/app/services/RolePlay/TaskService';
import AntButton from '@src/app/component/AntButton';
import {BUTTON, API} from '@constant';
import { toast } from '@component/ToastProvider';

const {Text, Paragraph} = Typography;
const {TextArea} = Input;

// Editable Cell Component (Slightly modified to handle different input types)
const EditableCell = ({
  editing,
  dataIndex,
  title,
  inputType,
  record,
  index,
  children,
  handleSave,
  toggleEdit, // Pass toggleEdit to cell
  ...restProps
}) => {
  const inputRef = useRef(null);
  const [form] = Form.useForm();
  const [isEditingThisCell, setIsEditingThisCell] = useState(false);

  useEffect(() => {
    if ((isEditingThisCell || editing) && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isEditingThisCell, editing]);

  useEffect(() => {
    if (editing && !isEditingThisCell) {
      setIsEditingThisCell(true);
      form.setFieldsValue({[dataIndex]: record[dataIndex]});
    }
  }, [editing, dataIndex, record, form, isEditingThisCell]);

  const startEdit = () => {
    setIsEditingThisCell(true);
    form.setFieldsValue({[dataIndex]: record[dataIndex]});
    if (toggleEdit) toggleEdit(record.key, dataIndex, true); // Notify parent about edit start
  };

  const save = async () => {
    try {
      const values = await form.validateFields();
      setIsEditingThisCell(false);
      if (handleSave) handleSave({...record, ...values});
      if (toggleEdit) toggleEdit(record.key, dataIndex, false); // Notify parent about edit end
    } catch (errInfo) {
      console.log('Save failed:', errInfo);
    }
  };

  let childNode = children;

  if (record) {
    // Ensure record exists
    if (editing || isEditingThisCell) {
      if (inputType === 'textarea') {
        childNode = (
          <Form form={form} component={false}>
            <Form.Item
              style={{margin: 0}}
              name={dataIndex}
              rules={[
                {required: title === 'Topic' || title === 'Evaluation guidelines', message: `${title} is required.`},
              ]}
            >
              <TextArea
                ref={inputRef}
                onPressEnter={e => {
                  e.preventDefault();
                  save();
                }}
                onBlur={save}
                autoSize={{minRows: 2, maxRows: 6}}
              />
            </Form.Item>
          </Form>
        );
      } else {
        // Default to Input
        childNode = (
          <Form form={form} component={false}>
            <Form.Item
              style={{margin: 0}}
              name={dataIndex}
              rules={[{required: title === 'Topic', message: `${title} is required.`}]}
            >
              <Input ref={inputRef} onPressEnter={save} onBlur={save} />
            </Form.Item>
          </Form>
        );
      }
    } else {
      childNode = (
        <div className="editable-cell-value-wrap" onClick={startEdit} style={{cursor: 'pointer'}}>
          {children || <Text type="secondary" italic>{`Click to add ${title.toLowerCase()}`}</Text>}
        </div>
      );
    }
  }

  return <td {...restProps}>{childNode}</td>;
};

export const TaskEvaluationDisplayCard = forwardRef(({
  dataSource = [],
  onTaskAdd,
  onTaskUpdate,
  onTaskDelete,
  courseId, // Nhận courseId từ parent component
  hideButtons = false, // Prop để ẩn buttons
  ...restProps
}, ref) => {
  const {t} = useTranslation();
  const [editingCellKey, setEditingCellKey] = useState(''); // To track which cell is being edited e.g. "rowKey-dataIndex"
  const [editedRows, setEditedRows] = useState({}); // Track modified rows with their data
  const [newRows, setNewRows] = useState({}); // Track new rows that need to be created
  const [originalData, setOriginalData] = useState({}); // Track original data for comparison
  const [newTaskId, setNewTaskId] = useState(null); // Keep track of the most recently added task
  const [isAIModalVisible, setIsAIModalVisible] = useState(false); // Trạng thái hiển thị modal tạo từ AI
  const [userPrompt, setUserPrompt] = useState(''); // Prompt người dùng nhập vào
  const [isGeneratingWithAI, setIsGeneratingWithAI] = useState(false); // Trạng thái đang tạo từ AI

  // Sync states với dataSource và track original data
  useEffect(() => {
    console.log('=== DATASOURCE CHANGED ===');
    console.log('DataSource length:', dataSource.length);
    console.log('DataSource items:', dataSource.map(item => ({ id: item._id, name: item.name })));

    // Track original data cho tasks đã lưu (không có temp ID)
    const updatedOriginalData = {};
    const updatedNewRows = {};

    dataSource.forEach(task => {
      if (task._id && task._id.toString().startsWith('temp_')) {
        // Task mới - thêm vào newRows nếu chưa có
        if (!newRows[task._id]) {
          updatedNewRows[task._id] = { ...task };
        } else {
          // Giữ lại data hiện có trong newRows (có thể đã được edit)
          updatedNewRows[task._id] = newRows[task._id];
        }
      } else {
        // Task đã lưu - track original data để so sánh
        updatedOriginalData[task._id] = { ...task };
      }
    });

    // Update states nếu có thay đổi
    if (Object.keys(updatedNewRows).length !== Object.keys(newRows).length ||
        Object.keys(updatedNewRows).some(key => !newRows[key])) {
      console.log('Syncing newRows with dataSource:', updatedNewRows);
      setNewRows(updatedNewRows);
    }

    if (Object.keys(updatedOriginalData).length !== Object.keys(originalData).length ||
        Object.keys(updatedOriginalData).some(key => !originalData[key])) {
      console.log('Syncing originalData with dataSource:', updatedOriginalData);
      setOriginalData(updatedOriginalData);
    }
  }, [dataSource]);

  // Helper function để check xem task có thay đổi so với dữ liệu gốc không
  const hasTaskChanged = (task) => {
    if (!task || !task._id) return false;

    // Task mới (temp ID) luôn được coi là có thay đổi nếu có nội dung
    if (task._id.toString().startsWith('temp_')) {
      return task.name && task.name.trim().length > 0;
    }

    // Task đã lưu - kiểm tra xem có trong editedRows không
    if (editedRows[task._id]) {
      // Nếu có trong editedRows, so sánh với dữ liệu gốc
      const editedTask = editedRows[task._id];
      const original = originalData[task._id];
      if (!original) return true; // Nếu không có original data, coi như có thay đổi

      // So sánh các field quan trọng
      const fieldsToCompare = ['name', 'description', 'evaluationGuidelines', 'weight', 'exampleVideoUrl', 'isMakeOrBreak'];

      for (const field of fieldsToCompare) {
        const currentValue = editedTask[field] || '';
        const originalValue = original[field] || '';

        if (currentValue !== originalValue) {
          return true;
        }
      }

      // So sánh helpfulLinks (array)
      const currentLinks = Array.isArray(editedTask.helpfulLinks) ? editedTask.helpfulLinks : [];
      const originalLinks = Array.isArray(original.helpfulLinks) ? original.helpfulLinks : [];

      if (currentLinks.length !== originalLinks.length ||
          currentLinks.some((link, index) => link !== originalLinks[index])) {
        return true;
      }
    }

    return false;
  };

  // Helper function để check unsaved tasks cho parent component
  const getUnsavedTasks = () => {
    const unsavedTasks = [];

    // Check new tasks
    Object.values(newRows).forEach(task => {
      if (task.name && task.name.trim().length > 0) {
        unsavedTasks.push(task);
      }
    });

    // Check edited existing tasks
    Object.values(editedRows).forEach(task => {
      if (hasTaskChanged(task)) {
        unsavedTasks.push(task);
      }
    });

    return unsavedTasks;
  };

  // Debug states changes
  useEffect(() => {
    console.log('=== STATES DEBUG ===');
    console.log('EditedRows:', Object.keys(editedRows));
    console.log('NewRows:', Object.keys(newRows));
    console.log('OriginalData:', Object.keys(originalData));
  }, [editedRows, newRows, originalData]);

  // Expose methods to parent component
  useImperativeHandle(ref, () => ({
    showAIModal: () => setIsAIModalVisible(true),
    getUnsavedTasks: getUnsavedTasks,
  }));

  // Kiểm tra xem một cell có đang ở chế độ chỉnh sửa không
  const isCellEditing = (record, dataIndex) => {
    return editingCellKey === `${record._id}-${dataIndex}` || record._id === newTaskId;
  };

  const handleToggleEdit = (rowKey, dataIndex, isEditing) => {
    setEditingCellKey(isEditing ? `${rowKey}-${dataIndex}` : '');

    // Khi bắt đầu edit, track task vào editedRows hoặc newRows
    if (isEditing) {
      const task = dataSource.find(item => item._id === rowKey);
      if (task) {
        if (task._id.toString().startsWith('temp_')) {
          // Task mới - update newRows
          setNewRows(prev => ({
            ...prev,
            [rowKey]: {...prev[rowKey], ...task},
          }));
        } else {
          // Task đã lưu - update editedRows
          setEditedRows(prev => ({
            ...prev,
            [rowKey]: {...prev[rowKey], ...task},
          }));
        }
      }
    }
  };

  const handleSave = updatedRecord => {
    // Xử lý helpfulLinks nếu là chuỗi (người dùng nhập vào)
    if (typeof updatedRecord.helpfulLinks === 'string') {
      updatedRecord.helpfulLinks = updatedRecord.helpfulLinks
        .split(',')
        .map(link => link.trim())
        .filter(Boolean);
    }

    // Notify parent component về update
    onTaskUpdate(updatedRecord);

    // Update appropriate state based on task type
    if (updatedRecord._id.toString().startsWith('temp_')) {
      // Task mới - update newRows
      setNewRows(prev => ({
        ...prev,
        [updatedRecord._id]: {...prev[updatedRecord._id], ...updatedRecord},
      }));
    } else {
      // Task đã lưu - update editedRows
      setEditedRows(prev => ({
        ...prev,
        [updatedRecord._id]: {...prev[updatedRecord._id], ...updatedRecord},
      }));
    }

    // Clear newTaskId if this is the task that was just added
    if (updatedRecord._id === newTaskId) {
      setNewTaskId(null);
    }
  };

  const handleRowSave = async record => {
    try {
      if (record._id.toString().startsWith('temp_')) {
        // New row - create
        if (!courseId) {
          toast.error(t('COURSE_ID_REQUIRED', 'Course ID is required'));
          return;
        }

        // scenarioId không cần thiết vì model đã xóa field này

        // Tính toán orderInScenario dựa vào số lượng task hiện có
        const currentTaskCount = dataSource.filter(task =>
          !task._id.toString().startsWith('temp_') // Chỉ đếm tasks đã được lưu
        ).length;
        const orderInScenario = currentTaskCount + 1;

        const dataToSave = {
          ...record,
          courseId, // Đảm bảo courseId được truyền vào khi tạo task mới
          orderInScenario, // Thêm thứ tự task trong kịch bản
        };

        // Đảm bảo helpfulLinks luôn là mảng
        if (typeof dataToSave.helpfulLinks === 'string') {
          dataToSave.helpfulLinks = dataToSave.helpfulLinks
            .split(',')
            .map(link => link.trim())
            .filter(Boolean);
        } else if (!Array.isArray(dataToSave.helpfulLinks)) {
          dataToSave.helpfulLinks = [];
        }

        delete dataToSave._id; // Remove temp id
        delete dataToSave.key; // Remove key

        try {
          const createdTask = await createRoleplayTask(dataToSave);
          if (!createdTask) {
            return;
          }

          // Update the task in parent component with the real _id from server
          // Truyền original_temp_id để parent có thể tìm và thay thế đúng task
          onTaskUpdate({...createdTask, original_temp_id: record._id, key: createdTask._id});

          // Remove from newRows state
          const updatedNewRows = {...newRows};
          delete updatedNewRows[record._id];
          setNewRows(updatedNewRows);

          // ALSO Remove from editedRows state if it exists for the tempId
          if (editedRows[record._id]) {
            const updatedEditedRows = {...editedRows};
            delete updatedEditedRows[record._id];
            setEditedRows(updatedEditedRows);
          }
        } catch (error) {
          console.error('Error creating task:', error);
          toast.error(t('CREATE_ERROR', 'Failed to create task'));
        }
      } else {
        // Existing row - update
        const dataToUpdate = {...record};

        // Đảm bảo helpfulLinks luôn là mảng
        if (typeof dataToUpdate.helpfulLinks === 'string') {
          dataToUpdate.helpfulLinks = dataToUpdate.helpfulLinks
            .split(',')
            .map(link => link.trim())
            .filter(Boolean);
        } else if (!Array.isArray(dataToUpdate.helpfulLinks)) {
          dataToUpdate.helpfulLinks = [];
        }

        try {
          const updatedTask = await updateRoleplayTask(dataToUpdate);
          toast.success(t('TASK_UPDATED', 'Task updated successfully'));

          // Update originalData với dữ liệu mới sau khi save thành công
          setOriginalData(prev => ({
            ...prev,
            [record._id]: { ...dataToUpdate }
          }));
        } catch (error) {
          console.error('Error updating task:', error);
          toast.error(t('UPDATE_ERROR', 'Failed to update task'));
        }

        // Remove from editedRows state
        const updatedEditedRows = {...editedRows};
        delete updatedEditedRows[record._id];
        setEditedRows(updatedEditedRows);
      }
    } catch (error) {
      console.error('Error saving task:', error);
      toast.error(t('SAVE_ERROR', 'Failed to save task'));
    }
  };

  const handleAddTask = () => {
    const newId = `temp_${Date.now()}`;
    const newTask = {
      _id: newId,
      name: '',
      description: '',
      evaluationGuidelines: '',
      weight: 0,
      exampleVideoUrl: '',
      helpfulLinks: [],
      isMakeOrBreak: false,
    };

    onTaskAdd(newTask);

    // Add to newRows
    setNewRows(prev => ({
      ...prev,
      [newTask._id]: newTask,
    }));

    // Set this as the new task being edited
    setNewTaskId(newId);
  };

  // Hiển thị modal nhập prompt AI
  const showAIModal = () => {
    setIsAIModalVisible(true);
    setUserPrompt('');
  };

  // Đóng modal nhập prompt AI
  const handleAIModalCancel = () => {
    setIsAIModalVisible(false);
    setUserPrompt('');
  };

  // Xử lý tạo task từ AI
  const handleGenerateWithAI = async () => {
    if (!courseId) {
      toast.error(t('COURSE_ID_REQUIRED', 'Course ID is required'));
      return;
    }

    // scenarioId không cần thiết vì model đã xóa field này

    if (!userPrompt.trim()) {
      toast.error(t('PROMPT_REQUIRED', 'Please enter a prompt'));
      return;
    }

    try {
      setIsGeneratingWithAI(true);
      const result = await createTasksFromPrompt(courseId, userPrompt.trim());

      // Xử lý nhiều định dạng response có thể từ AI
      let tasksArray = [];

      if (result && result.tasks && Array.isArray(result.tasks)) {
        tasksArray = result.tasks;
      } else if (result && Array.isArray(result)) {
        tasksArray = result;
      } else if (result && result.data && Array.isArray(result.data)) {
        tasksArray = result.data;
      } else if (result && result.data && result.data.tasks && Array.isArray(result.data.tasks)) {
        tasksArray = result.data.tasks;
      } else if (result && typeof result === 'object' && result.name) {
        tasksArray = [result];
      } else {
        // Thử tìm tasks trong các nested properties
        const findTasks = (obj, path = '') => {
          if (Array.isArray(obj)) {
            return obj;
          }
          if (obj && typeof obj === 'object') {
            for (const [key, value] of Object.entries(obj)) {
              if (key.toLowerCase().includes('task') && Array.isArray(value)) {
                return value;
              }
              const nested = findTasks(value, `${path}.${key}`);
              if (nested) return nested;
            }
          }
          return null;
        };

        const foundTasks = findTasks(result);
        if (foundTasks) {
          tasksArray = foundTasks;
        }
      }

      if (tasksArray.length > 0) {
        // Tính toán orderInScenario cho các task từ AI
        const currentTaskCount = dataSource.filter(task =>
          !task._id.toString().startsWith('temp_') // Chỉ đếm tasks đã được lưu
        ).length;

        // Thêm các task mới vào danh sách
        const newTasks = tasksArray.map((task, index) => {
          const newId = `temp_${Date.now()}_${index}`;
          const processedTask = {
            name: task.name || task.topic || task.title || `Task ${index + 1}`,
            description: task.description || task.desc || task.content || '',
            evaluationGuidelines: task.evaluationGuidelines || task.guidelines || task.criteria || task.evaluation || '',
            weight: parseInt(task.weight) || parseInt(task.score) || parseInt(task.points) || 0,
            exampleVideoUrl: task.exampleVideoUrl || task.videoUrl || task.video || '',
            helpfulLinks: task.helpfulLinks || task.links || task.resources || [],
            isMakeOrBreak: Boolean(task.isMakeOrBreak || task.critical || task.required),
            _id: newId,
            key: newId,
            orderInScenario: currentTaskCount + index + 1, // Thêm thứ tự cho task từ AI
          };

          return processedTask;
        });


        // Thêm các task mới vào state
        const newRowsToAdd = {};
        newTasks.forEach((task, index) => {
          newRowsToAdd[task._id] = task;
        });


        setNewRows(prev => {
          const updated = {
            ...prev,
            ...newRowsToAdd,
          };
          return updated;
        });

        // Thay vì gọi onTaskAdd từng task riêng biệt, gọi một lần với tất cả tasks
        if (onTaskAdd && typeof onTaskAdd === 'function') {
          // Gọi onTaskAdd với một object chứa tất cả tasks
          onTaskAdd({
            type: 'BULK_ADD',
            tasks: newTasks
          });
        } else {
          // Fallback: gọi từng task riêng biệt nếu parent không hỗ trợ bulk add
          newTasks.forEach((task, index) => {
            if (onTaskAdd) onTaskAdd(task);
          });
        }

        toast.success(t('TASKS_GENERATED', `Đã tạo thành công ${newTasks.length} nhiệm vụ từ AI`));
        setIsAIModalVisible(false);
        setUserPrompt(''); // Reset prompt
      } else {
        console.warn('No valid tasks found in AI response:', result);
        toast.error(t('GENERATE_ERROR', 'AI không trả về nhiệm vụ hợp lệ. Vui lòng thử lại với prompt khác.'));
      }
    } catch (error) {
      console.error('Error generating tasks:', error);
      toast.error(t('GENERATE_ERROR', 'Không thể tạo nhiệm vụ từ AI. Vui lòng thử lại.'));
    } finally {
      setIsGeneratingWithAI(false);
    }
  };

  const handleDeleteTask = async taskId => {
    try {
      // Nếu là task tạm thời (chưa lưu vào DB)
      if (taskId.toString().startsWith('temp_')) {
        onTaskDelete(taskId);
        return;
      }

      // Hiển thị modal xác nhận xóa
      Modal.confirm({
        title: t('CONFIRM_DELETE_TASK', 'Are you sure you want to delete this task?'),
        content: t('DELETE_TASK_WARNING', 'This action cannot be undone.'),
        okText: t('DELETE', 'Delete'),
        okType: 'danger',
        cancelText: t('CANCEL', 'Cancel'),
        onOk: async () => {
          try {
            await deleteRoleplayTask(taskId);
            toast.success(t('TASK_DELETED', 'Task deleted successfully'));
            onTaskDelete(taskId);
          } catch (error) {
            console.error('Error deleting task:', error);
            toast.error(t('DELETE_ERROR', 'Failed to delete task'));
          }
        },
      });
    } catch (error) {
      console.error('Error in handleDeleteTask:', error);
      toast.error(t('DELETE_ERROR', 'Failed to delete task'));
    }
  };

  const columns = [
    {
      title: (
        <Tooltip title={t('TASK_TOPIC_TOOLTIP', 'The main subject.')}>
          <Text strong>{t('TOPIC_COLUMN', 'Topic')}</Text> <QuestionCircleOutlined />
        </Tooltip>
      ),
      dataIndex: 'name',
      key: 'name',
      width: '12%',
      editable: true,
      inputType: 'textarea',
      render: text =>
        text ? (
          <Paragraph style={{whiteSpace: 'pre-line', marginBottom: 0, lineHeight: '1.5'}}>{text}</Paragraph>
        ) : (
          <Text type="secondary" italic>{`Click to add ${t('TOPIC_COLUMN', 'Topic')}`}</Text>
        ),
    },
    {
      title: (
        <Tooltip title={t('EVALUATION_GUIDELINES_TOOLTIP', 'Criteria for evaluation.')}>
          <Text strong>{t('EVALUATION_GUIDELINES_COLUMN', 'Evaluation guidelines')}</Text> <QuestionCircleOutlined />
        </Tooltip>
      ),
      dataIndex: 'evaluationGuidelines',
      key: 'evaluationGuidelines',
      width: '24%',
      editable: true,
      inputType: 'textarea',
      render: text =>
        text ? (
          <Paragraph style={{whiteSpace: 'pre-line', marginBottom: 0, lineHeight: '1.5'}}>{text}</Paragraph>
        ) : (
          <Text type="secondary" italic>{`Click to add ${t(
            'EVALUATION_GUIDELINES_COLUMN',
            'Evaluation guidelines',
          )}`}</Text>
        ),
    },
    {
      title: (
        <Tooltip title={t('WEIGHT_TOOLTIP', 'Importance percentage.')}>
          <Text strong>{t('WEIGHT_COLUMN', 'Weight')}</Text> <QuestionCircleOutlined />
        </Tooltip>
      ),
      dataIndex: 'weight',
      key: 'weight',
      width: '8%',
      align: 'center',
      render: (text, record) => (
        <InputNumber
          min={0}
          max={100}
          value={text} // Use value instead of defaultValue for controlled component behavior
          formatter={value => `${value}%`}
          parser={value => String(value).replace('%', '')}
          onChange={value => {
            const updatedRecord = {...record, weight: value};
            handleSave(updatedRecord);
          }}
          style={{width: '100%', maxWidth: '80px'}}
        />
      ),
    },
    {
      title: (
        <Tooltip title={t('EXAMPLE_VIDEOS_TOOLTIP', 'Link to example video.')}>
          <Text strong>{t('EXAMPLE_VIDEOS_COLUMN', 'Example videos')}</Text> <QuestionCircleOutlined />
        </Tooltip>
      ),
      dataIndex: 'exampleVideoUrl',
      key: 'exampleVideoUrl',
      width: '12%',
      editable: true,
      inputType: 'textarea',
      render: text =>
        text ? (
          <Paragraph style={{whiteSpace: 'pre-line', marginBottom: 0, lineHeight: '1.5'}}>{text}</Paragraph>
        ) : (
          <Text type="secondary" italic>{`Click to add ${t('HELPFUL_LINKS_COLUMN', 'Helpful links')}`}</Text>
        ),
    },
    {
      title: (
        <Tooltip title={t('HELPFUL_LINKS_TOOLTIP', 'Link to helpful resources. Separate multiple links with commas.')}>
          <Text strong>{t('HELPFUL_LINKS_COLUMN', 'Helpful links')}</Text> <QuestionCircleOutlined />
        </Tooltip>
      ),
      dataIndex: 'helpfulLinks',
      key: 'helpfulLinks',
      width: '12%',
      editable: true,
      inputType: 'textarea',
      render: links => {
        if (!links || (Array.isArray(links) && links.length === 0)) {
          return (
            <Text type="secondary" italic>
              Enter links separated by commas
            </Text>
          );
        }
        return Array.isArray(links) ? links.join(', ') : links;
      },
    },
    {
      title: (
        <Tooltip title={t('MAKE_OR_BREAK_TOOLTIP', 'Critical for passing.')}>
          <Text strong>{t('MAKE_OR_BREAK_COLUMN', 'Make or Break')}</Text> <QuestionCircleOutlined />
        </Tooltip>
      ),
      dataIndex: 'isMakeOrBreak',
      key: 'isMakeOrBreak',
      width: '8%',
      align: 'center',
      render: (isMakeOrBreak, record) => (
        <Switch
          checked={isMakeOrBreak}
          onChange={checked => {
            const updatedRecord = {...record, isMakeOrBreak: checked};
            handleSave(updatedRecord);
          }}
        />
      ),
    },
    {
      title: t('ACTIONS_COLUMN', 'Actions'),
      key: 'actions',
      width: '8%',
      align: 'center',
      render: (_, record) => {
        // Logic mới cho enable/disable nút Save
        let canSave = false;
        let saveTooltip = t('SAVE_TASK_TOOLTIP', 'Save this task/topic');

        if (record._id && record._id.toString().startsWith('temp_')) {
          // Task mới - enable nếu có tên
          canSave = record.name && record.name.trim().length > 0;
          if (!canSave) {
            saveTooltip = t('TASK_NAME_REQUIRED', 'Task name is required to save');
          }
        } else {
          // Task đã lưu - enable nếu có thay đổi
          canSave = hasTaskChanged(record);
          if (!canSave) {
            saveTooltip = t('NO_CHANGES_TO_SAVE', 'No changes to save');
          }
        }

        return (
          <Space>
            <Tooltip title={saveTooltip}>
              <Button
                type="primary"
                size="small"
                icon={<SaveOutlined />}
                onClick={() => handleRowSave(record)}
                disabled={!canSave}
              />
            </Tooltip>
            <Tooltip title={t('DELETE_TASK_TOOLTIP', 'Delete this task/topic')}>
              <Button
                type="text"
                danger
                size="small"
                icon={<DeleteOutlined />}
                onClick={() => handleDeleteTask(record._id)}
              />
            </Tooltip>
          </Space>
        );
      },
    },
  ];

  const mergedColumns = columns.map(col => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: record => ({
        record,
        inputType: col.inputType || 'input',
        dataIndex: col.dataIndex,
        title: col.title,
        editing: isCellEditing(record, col.dataIndex),
        handleSave: handleSave,
        toggleEdit: handleToggleEdit,
      }),
    };
  });

  return (
    <div className="task-evaluation-display-card" style={restProps.style}>
      {!hideButtons && (
        <Space style={{marginBottom: 16, marginLeft: 16, marginTop: 16, justifyContent: 'space-between', alignSelf:'end'}}>
          <AntButton onClick={handleAddTask} size={'large'} icon={<PlusOutlined />} type={BUTTON.DEEP_NAVY}>
            {t('ADD_TASK_TOPIC', 'Add Topic / Task')}
          </AntButton>
          <AntButton
            onClick={showAIModal}
            icon={<RobotOutlined />}
            disabled={!courseId}
            type={'primary'}
            style={{background: '#722ED1', marginRight: 8}}
            size={'large'}
            data-testid="ai-create-button"
          >
            {t('CREATE_FROM_AI', 'Create from AI')}
          </AntButton>
        </Space>
      )}

      {/* Modal nhập prompt AI */}
      <Modal
        title={t('CREATE_TASKS_FROM_AI', 'Create Tasks from AI')}
        open={isAIModalVisible}
        onCancel={handleAIModalCancel}
        footer={[
          <AntButton key="cancel" onClick={handleAIModalCancel} size={'large'}>
            {t('CANCEL', 'Cancel')}
          </AntButton>,
          <AntButton
            key="generate"
            type="primary"
            loading={isGeneratingWithAI}
            onClick={handleGenerateWithAI}
            style={{background: '#722ED1'}}
            size={'large'}
          >
            {t('GENERATE', 'Generate')}
          </AntButton>,
        ]}
      >
        <Form layout="vertical">
          <Form.Item
            label={t('PROMPT_LABEL', 'Describe the tasks you want to generate')}
            help={t('PROMPT_HELP', 'For example: "conversation between a student and a potential customer"')}
          >
            <TextArea
              rows={4}
              value={userPrompt}
              onChange={e => setUserPrompt(e.target.value)}
              placeholder={t('PROMPT_PLACEHOLDER', 'Enter your prompt here...')}
            />
          </Form.Item>
        </Form>
      </Modal>

      <div className="table-container">
        {(() => {
          const tableDataSource = dataSource.map(task => ({...task, key: task._id}));
          console.log('=== TABLE RENDER ===');
          console.log('Table dataSource:', tableDataSource);
          console.log('Table dataSource length:', tableDataSource.length);
          return (
            <Table
              components={{
                body: {
                  cell: EditableCell,
                },
              }}
              rowClassName="editable-row" // No function needed if it's just a class name
              columns={mergedColumns}
              dataSource={tableDataSource} // Ensure key is present
              pagination={false}
              // scroll={{y: 400}}
              className="evaluation-table"
              bordered
            />
          );
        })()}
      </div>
    </div>
  );
});

TaskEvaluationDisplayCard.displayName = 'TaskEvaluationDisplayCard';
